""" 支持网络ADB的Android设备工具类 """
import subprocess
import os
import shlex
import logging
from typing import Optional, Tuple, List
from PIL import Image
from tempfile import mktemp
import json

# 获取ADB路径
def get_adb_path():
    """获取ADB可执行文件的路径"""
    current_dir = os.path.dirname(os.path.abspath(__file__))

    # 检查不同操作系统的ADB文件
    if os.name == 'nt':  # Windows
        adb_path = os.path.join(current_dir, 'platform-tools', 'adb.exe')
    else:  # Linux/Mac
        adb_path = os.path.join(current_dir, 'platform-tools', 'adb')
        # 如果没有找到，尝试使用系统的adb
        if not os.path.exists(adb_path):
            adb_path = '/home/<USER>/Personal_Projects/agent_vl/agent_vl/platform-tools/adb.exe'

    return adb_path

ADB_PATH = get_adb_path()

class AdbResult:
    def __init__(self, succeed: bool, output: str, error: str):
        self.succeed = succeed
        self.output = output
        self.error = error

class NetworkAndroidDevice:
    def __init__(self, serial: str, host_ip: str = None, device_alias: str = "网络Android设备"):
        """
        初始化网络Android设备
        :param serial: 设备序列号 (如 emulator-5554)
        :param host_ip: 主机IP地址 (如 *************)
        :param device_alias: 设备别名
        """
        self.serial = serial
        self.host_ip = host_ip
        self.alias = device_alias
        self.logger = logging.getLogger(__name__)
        
        # 构建ADB命令前缀
        if self.host_ip:
            self.adb_prefix = f"{ADB_PATH} -H {self.host_ip} -s {self.serial}"
        else:
            self.adb_prefix = f"{ADB_PATH} -s {self.serial}"

    def adb(self, command: str) -> AdbResult:
        """
        执行adb命令
        :param command: adb命令
        :return: AdbResult对象
        """
        full_command = f"{self.adb_prefix} {command}"
        try:
            proc = subprocess.run(
                full_command,
                shell=True,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                timeout=30
            )
            output = proc.stdout.decode('utf-8', errors="ignore").strip()
            errors = proc.stderr.decode('utf-8', errors="ignore").strip()
            
            return AdbResult(proc.returncode == 0, output, errors)
        except subprocess.TimeoutExpired:
            return AdbResult(False, "", "Command timeout")
        except Exception as e:
            return AdbResult(False, "", str(e))

    def execute_adb_command(self, command: str) -> Tuple[int, str, str]:
        """执行adb shell命令，返回(exit_code, stdout, stderr)"""
        # 如果命令以adb开头，替换为网络ADB命令
        if command.startswith('adb '):
            command = command.replace('adb ', f"{self.adb_prefix} ", 1)
        elif not command.startswith(self.adb_prefix):
            # 如果不是完整命令，添加前缀
            command = f"{self.adb_prefix} {command}"

        self.logger.info(f"[NetworkAndroidDevice] Executing command: {command}")
        try:
            proc = subprocess.Popen(
                command,
                shell=True,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            stdout, stderr = proc.communicate()
            
            # 处理编码问题
            try:
                stdout_str = stdout.decode('utf-8').strip()
            except UnicodeDecodeError:
                stdout_str = stdout.decode('gbk', errors='ignore').strip()
                
            try:
                stderr_str = stderr.decode('utf-8').strip()
            except UnicodeDecodeError:
                stderr_str = stderr.decode('gbk', errors='ignore').strip()
                
            return proc.returncode, stdout_str, stderr_str
        except Exception as e:
            self.logger.error(f"[NetworkAndroidDevice] Error executing command: {e}")
            return -1, "", str(e)

    def check_device_connected(self) -> bool:
        """检查网络ADB设备是否连接"""
        try:
            if self.host_ip:
                command = f"{ADB_PATH} -H {self.host_ip} devices"
            else:
                command = f"{ADB_PATH} devices"

            proc = subprocess.run(
                command,
                shell=True,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                timeout=10
            )

            if proc.returncode == 0:
                output = proc.stdout.decode('utf-8', errors="ignore").strip()
                if "List of devices attached" in output and self.serial in output and "device" in output:
                    lines = output.split('\n')
                    for line in lines[1:]:
                        if self.serial in line and "device" in line:
                            return True
            return False
        except Exception as e:
            self.logger.error(f"设备连接检查失败: {e}")
            return False

    @classmethod
    def getNetworkDevice(cls, host_ip: str, serial: str = "emulator-5554", alias: str = "网络Android设备") -> Optional['NetworkAndroidDevice']:
        """
        获取网络Android设备
        :param host_ip: 主机IP地址
        :param serial: 设备序列号
        :param alias: 设备别名
        :return: NetworkAndroidDevice对象或None
        """
        print(f"🔍 尝试连接网络设备: {host_ip}:{serial}")
        device = cls(serial, host_ip, alias)

        # 测试连接
        print(f"📡 测试设备连接...")
        if device.check_device_connected():
            print(f"✅ 网络设备连接成功: {host_ip}:{serial}")
            return device
        else:
            print(f"❌ 无法连接到网络设备 {host_ip}:{serial}")

            # 调试信息
            try:
                command = f"{ADB_PATH} -H {host_ip} devices"
                proc = subprocess.run(command, shell=True, capture_output=True, text=True, timeout=10)
                print(f"🔍 调试 - 命令: {command}")
                print(f"🔍 调试 - 返回码: {proc.returncode}")
                print(f"🔍 调试 - 输出: {proc.stdout}")
                if proc.stderr:
                    print(f"🔍 调试 - 错误: {proc.stderr}")
            except Exception as e:
                print(f"🔍 调试失败: {e}")

            return None

    def screenshot(self, output_path: str = "screen.png") -> str:
        """截取屏幕截图"""
        if not self.check_device_connected():
            return "Error: No network ADB device connected"

        try:
            # 使用网络ADB截图命令 - 直接使用subprocess而不是execute_adb_command
            if self.host_ip:
                command = f"{ADB_PATH} -H {self.host_ip} -s {self.serial} exec-out screencap -p > {output_path}"
            else:
                command = f"{ADB_PATH} -s {self.serial} exec-out screencap -p > {output_path}"

            proc = subprocess.run(
                command,
                shell=True,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                timeout=15
            )

            if proc.returncode == 0 and os.path.exists(output_path):
                file_size = os.path.getsize(output_path)
                return f"Screenshot saved to {output_path} (size: {file_size} bytes)"
            else:
                error_msg = proc.stderr.decode('utf-8', errors="ignore").strip()
                return f"Error taking screenshot: {error_msg}"

        except Exception as e:
            return f"Error taking screenshot: {str(e)}"

    def click(self, point_x: float, point_y: float) -> str:
        """在指定坐标进行点击操作"""
        if not self.check_device_connected():
            return "Error: No network ADB device connected"
        
        command = f"shell input tap {int(point_x)} {int(point_y)}"
        returncode, stdout, stderr = self.execute_adb_command(command)
        
        if returncode == 0:
            return f"Clicked at ({point_x}, {point_y}) successfully."
        else:
            return f"Error clicking at ({point_x}, {point_y}): {stderr}"

    def long_click(self, point_x: float, point_y: float, duration: int = 1000) -> str:
        """长按指定坐标"""
        if not self.check_device_connected():
            return "Error: No network ADB device connected"
        
        command = f"shell input swipe {int(point_x)} {int(point_y)} {int(point_x)} {int(point_y)} {duration}"
        returncode, stdout, stderr = self.execute_adb_command(command)
        
        if returncode == 0:
            return f"Long clicked at ({point_x}, {point_y}) successfully."
        else:
            return f"Error long clicking at ({point_x}, {point_y}): {stderr}"

    def swipe(self, direction: str, distance: int = 500, duration: int = 300) -> str:
        """滑动操作"""
        if not self.check_device_connected():
            return "Error: No network ADB device connected"
        
        # 获取屏幕尺寸
        ret = self.adb("shell wm size")
        if not ret.succeed:
            return "Error: Cannot get screen size"
        
        try:
            size_line = ret.output.split('\n')[0]
            width, height = map(int, size_line.split(': ')[1].split('x'))
        except:
            width, height = 1080, 1920  # 默认尺寸
        
        center_x, center_y = width // 2, height // 2
        
        if direction == "up":
            start_x, start_y = center_x, center_y + distance // 2
            end_x, end_y = center_x, center_y - distance // 2
        elif direction == "down":
            start_x, start_y = center_x, center_y - distance // 2
            end_x, end_y = center_x, center_y + distance // 2
        elif direction == "left":
            start_x, start_y = center_x + distance // 2, center_y
            end_x, end_y = center_x - distance // 2, center_y
        elif direction == "right":
            start_x, start_y = center_x - distance // 2, center_y
            end_x, end_y = center_x + distance // 2, center_y
        else:
            return f"Error: Invalid direction {direction}"
        
        command = f"shell input swipe {start_x} {start_y} {end_x} {end_y} {duration}"
        returncode, stdout, stderr = self.execute_adb_command(command)
        
        if returncode == 0:
            return f"Swiped {direction} successfully."
        else:
            return f"Error swiping {direction}: {stderr}"

    def go_back(self) -> str:
        """返回上一级操作"""
        if not self.check_device_connected():
            return "Error: No network ADB device connected"
        
        command = "shell input keyevent KEYCODE_BACK"
        returncode, stdout, stderr = self.execute_adb_command(command)
        
        if returncode == 0:
            return "Back key event sent successfully."
        else:
            return f"Error sending Back key event: {stderr}"

    def go_home(self) -> str:
        """返回主页面操作"""
        if not self.check_device_connected():
            return "Error: No network ADB device connected"
        
        command = "shell input keyevent KEYCODE_HOME"
        returncode, stdout, stderr = self.execute_adb_command(command)
        
        if returncode == 0:
            return "Home key event sent successfully."
        else:
            return f"Error sending Home key event: {stderr}"

    def input_text(self, x: float, y: float, text: str) -> str:
        """在指定坐标输入文字"""
        if not self.check_device_connected():
            return "Error: No network ADB device connected"
        
        click_result = self.click(x, y)
        if "Error" in click_result:
            return f"Error activating input field: {click_result}"
        
        text_quoted = shlex.quote(text)
        command = f"shell input text {text_quoted}"
        returncode, stdout, stderr = self.execute_adb_command(command)
        
        if returncode == 0:
            return f"Input text '{text}' at ({x}, {y}) successfully."
        else:
            return f"Error inputting text at ({x}, {y}): {stderr}"

    def fling(self, start_point: Tuple[float, float], end_point: Tuple[float, float],
              swipe_velocity_pps: Optional[int] = 300) -> str:
        """模拟快滑操作"""
        if not self.check_device_connected():
            return "Error: No network ADB device connected"
        
        from_x, from_y = start_point
        to_x, to_y = end_point
        command = f"shell input swipe {from_x} {from_y} {to_x} {to_y} {swipe_velocity_pps}"
        
        returncode, stdout, stderr = self.execute_adb_command(command)
        if returncode == 0:
            return f"Flung {start_point}->{end_point} successfully."
        else:
            return f"Error flinging {start_point}->{end_point}: {stderr}"

    def drag(self, start_point: Tuple[float, float], end_point: Tuple[float, float],
             duration: int = 1000) -> str:
        """模拟拖拽操作"""
        if not self.check_device_connected():
            return "Error: No network ADB device connected"
        
        from_x, from_y = start_point
        to_x, to_y = end_point
        command = f"shell input swipe {from_x} {from_y} {to_x} {to_y} {duration}"
        
        returncode, stdout, stderr = self.execute_adb_command(command)
        if returncode == 0:
            return f"Dragged {start_point}->{end_point} successfully."
        else:
            return f"Error dragging {start_point}->{end_point}: {stderr}"

# 为了兼容性，创建一个别名
AndroidDevice = NetworkAndroidDevice
