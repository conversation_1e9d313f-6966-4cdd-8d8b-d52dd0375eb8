# Agent VL - 智能手机控制Agent

这是一个基于视觉语言模型的智能手机自动化控制项目，能够通过自然语言指令自动操作Android设备。

## 项目概述

Agent VL 是一个多模态AI Agent，结合了计算机视觉、自然语言处理和Android自动化技术，能够：
- 理解用户的自然语言指令
- 分析手机屏幕截图
- 自动识别UI元素
- 执行相应的操作（点击、滑动、输入等）

## 项目架构

### 核心组件

1. **agent.py** - 主要的Agent控制器
   - `AppUseAgent` 类：核心的智能控制代理
   - 集成视觉模型和操作执行
   - 任务状态管理和历史记录

2. **tooluse.py** - Android设备操作工具
   - `AndroidDevice` 类：封装ADB命令
   - 提供点击、滑动、截图等基础操作
   - 设备连接和状态管理

3. **grounding/** - 视觉定位模块
   - `grounding.py`：UI元素检测和定位
   - 基于YOLO模型的目标检测
   - 生成可交互元素的边界框

4. **show_coordinate.py** - 坐标标记工具
   - 在截图上标记操作点
   - 用于调试和可视化

### 目录结构

```
agent_vl/
├── agent.py                    # 主Agent控制器
├── tooluse.py                  # Android设备操作工具
├── show_coordinate.py          # 坐标标记工具
├── requirements.txt            # 依赖包列表
├── grounding/                  # 视觉定位模块
│   ├── grounding.py           # UI元素检测
│   ├── util/                  # 工具函数
│   │   ├── box_annotator.py   # 边界框标注
│   │   └── utils.py           # 通用工具
│   └── ours_yolo_model/       # YOLO模型文件
│       └── yolov8.pt          # 预训练模型
└── platform-tools/            # ADB工具包
    ├── adb.exe                # ADB可执行文件
    └── ...                    # 其他ADB相关文件
```

## 技术栈

- **深度学习框架**: PyTorch, Ultralytics YOLO
- **视觉处理**: OpenCV, PIL, Supervision
- **语言模型**: OpenAI API (支持多种模型)
- **Android自动化**: ADB (Android Debug Bridge)
- **数据处理**: Pydantic, NumPy

## 安装和配置

### 1. 环境要求

- Python 3.8+
- Android设备（开启USB调试）
- ADB工具（已包含在项目中）

### 2. 安装依赖

```bash
pip install -r requirements.txt
```

### 3. 设备配置

1. 在Android设备上开启"开发者选项"
2. 启用"USB调试"
3. 连接设备到电脑
4. 确认ADB连接：
   ```bash
   adb devices
   ```

### 4. API配置

在 `agent.py` 中配置OpenAI API：

```python
openai_client = OpenAI(
    base_url="your_api_base_url",
    api_key="your_api_key"
)
model_name = "your_model_name"  # 如 "qwen/qwen2.5-vl-72b-instruct"
```

## 使用方法

### 🌐 图形界面使用 ⭐ **推荐**

#### 启动实时界面

```bash
python realtime_ui.py
```

或使用启动脚本（自动检查依赖）：

```bash
python start_ui.py
```

界面将在浏览器中打开：`http://localhost:7861`

#### 界面功能

- **📝 任务输入**: 输入自然语言指令
- **🚀 一键执行**: 点击开始按钮启动任务
- **⚡ 实时更新**: 每2秒自动刷新状态、进度和截图
- **📱 实时截图**: 自动显示当前设备屏幕
- **📋 详细日志**: 完整的执行过程记录
- **⏹️ 随时停止**: 支持中途停止任务

#### 使用示例

1. 在任务输入框中输入：`"打开微信，发送消息给张三"`
2. 点击"🚀 开始"按钮
3. 界面会自动显示执行进度和实时截图
4. 任务完成后查看执行日志

### 💻 命令行使用（高级用户）

```python
from agent import AppUseAgent
from openai import OpenAI

# 初始化客户端
openai_client = OpenAI(
    base_url="https://openrouter.ai/api/v1",
    api_key="your_api_key"
)

# 创建Agent
agent = AppUseAgent(
    openai_client=openai_client,
    model_name="qwen/qwen2.5-vl-72b-instruct",
    max_rounds=30
)

# 执行任务
task = "打开微信，找到张三的聊天记录"
agent.run(task)
```

### 支持的操作类型

- **Click**: 点击指定元素
- **LongClick**: 长按指定元素
- **Swipe**: 方向性滑动（上下左右）
- **Fling**: 快速滑动（用于滚动列表）
- **Drag**: 拖拽操作（用于滑块等）
- **InputText**: 文本输入
- **GoBack**: 返回上一级
- **GoHome**: 返回主页

### 任务示例

```python
# 示例任务
tasks = [
    "打开设置，关闭蓝牙",
    "打开相册，查看最新的照片",
    "发送微信消息给张三：今天天气不错",
    "在淘宝搜索iPhone 15",
    "调整屏幕亮度到50%"
]

for task in tasks:
    agent.run(task)
```

## 工作原理

### 1. 任务执行流程

1. **截图获取**: 通过ADB获取设备当前屏幕
2. **UI分析**: 使用YOLO模型检测可交互元素
3. **指令理解**: 大语言模型分析用户指令和屏幕状态
4. **动作决策**: 确定下一步操作类型和目标元素
5. **操作执行**: 通过ADB执行具体操作
6. **状态更新**: 记录操作历史，判断任务完成状态

### 2. 核心算法

- **目标检测**: YOLOv8模型识别UI元素
- **多模态理解**: 视觉-语言模型分析屏幕和指令
- **决策推理**: 基于当前状态和历史操作的智能决策

## 输出和日志

Agent运行时会生成以下文件：

- `operation_log.txt`: 操作记录日志
- `prompt.txt`: 模型输入提示记录
- `round{N}_image.jpg`: 每轮的屏幕截图
- `round{N}_grounded.jpg`: 标注了UI元素的截图
- `round{N}_image_marked.jpg`: 标记了操作点的截图

## 注意事项

1. **设备兼容性**: 主要支持Android设备
2. **网络要求**: 需要稳定的网络连接访问语言模型API
3. **权限设置**: 确保ADB调试权限正确配置
4. **模型选择**: 推荐使用支持视觉的大语言模型
5. **安全考虑**: 避免在重要应用中进行测试

## 故障排除

### 常见问题

1. **设备连接失败**
   - 检查USB调试是否开启
   - 确认设备授权ADB连接
   - 重启ADB服务：`adb kill-server && adb start-server`

2. **模型响应错误**
   - 检查API密钥和网络连接
   - 确认模型支持视觉输入
   - 调整请求参数和提示词

3. **操作执行失败**
   - 检查屏幕元素是否正确识别
   - 调整YOLO模型的检测阈值
   - 验证坐标计算的准确性

## 扩展开发

项目采用模块化设计，支持以下扩展：

- 添加新的操作类型
- 集成其他视觉模型
- 支持iOS设备
- 增加语音控制功能
- 优化UI元素识别精度

## 许可证

本项目仅供学习和研究使用，请遵守相关法律法规和设备使用条款。
