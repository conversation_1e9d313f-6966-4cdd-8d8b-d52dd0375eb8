#!/usr/bin/env python3
"""
Agent VL 启动脚本
"""

import subprocess
import sys
import os

def check_and_install_gradio():
    """检查并安装Gradio"""
    try:
        import gradio
        print("✅ Gradio已安装")
        return True
    except ImportError:
        print("📦 正在安装Gradio...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "gradio"])
            print("✅ Gradio安装完成")
            return True
        except subprocess.CalledProcessError:
            print("❌ Gradio安装失败")
            return False

def main():
    """主函数"""
    print("🚀 Agent VL 启动器")
    print("=" * 40)
    
    # 检查Gradio
    if not check_and_install_gradio():
        print("❌ 无法安装Gradio，请手动安装: pip install gradio")
        return
    
    # 启动实时界面
    print("🌐 启动实时Gradio界面...")
    try:
        from realtime_ui import main as ui_main
        ui_main()
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        print("请确保所有依赖都已正确安装")

if __name__ == "__main__":
    main()
