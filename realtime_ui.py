#!/usr/bin/env python3
"""
Agent VL 实时更新界面 - 使用现代Gradio特性
"""

import gradio as gr
import threading
import time
import os
import glob
import asyncio
from openai import OpenAI
from agent import AppUseAgent

class RealtimeAgentUI:
    def __init__(self):
        self.agent = None
        self.is_running = False
        self.current_log = ""
        self.stop_flag = False
        self.update_queue = []
        
        # 配置
        self.config = {
            "base_url": "https://dashscope.aliyuncs.com/compatible-mode/v1",
            "api_key": "sk-397dece0bf07419d9a33b95ea038ca1d",
            "model_name": "qwen2.5-vl-72b-instruct"
        }

        # 网络ADB配置
        self.network_config = {
            "host_ip": "*************",
            "serial": "emulator-5554",
            "use_network": True
        }
    
    def create_interface(self):
        """创建实时更新界面"""
        
        with gr.<PERSON>s(title="Agent VL - 实时版", theme=gr.themes.Soft()) as interface:
            
            gr.<PERSON><PERSON>("# 🤖 Agent VL - 实时控制界面")
            gr.Markdown("⚡ **实时自动更新** - 无需手动刷新")

            # 配置区域
            with gr.Accordion("⚙️ 系统配置", open=False):
                with gr.Row():
                    # 模型选择
                    model_dropdown = gr.Dropdown(
                        label="🤖 AI模型",
                        choices=[
                            "qwen2.5-vl-72b-instruct",
                            "qwen2-vl-72b-instruct",
                            "qwen-vl-max",
                            "qwen-vl-plus",
                            "qvq-plus"
                        ],
                        value=self.config["model_name"],
                        interactive=True
                    )

                    # API配置
                    api_base_input = gr.Textbox(
                        label="🔗 API Base URL",
                        value=self.config["base_url"],
                        placeholder="https://dashscope.aliyuncs.com/compatible-mode/v1"
                    )

                with gr.Row():
                    # 网络ADB配置
                    host_ip_input = gr.Textbox(
                        label="🌐 主机IP地址",
                        value=self.network_config["host_ip"],
                        placeholder="*************"
                    )
                    serial_input = gr.Textbox(
                        label="📱 设备序列号",
                        value=self.network_config["serial"],
                        placeholder="emulator-5554"
                    )
                    use_network_checkbox = gr.Checkbox(
                        label="使用网络ADB",
                        value=self.network_config["use_network"]
                    )
            
            with gr.Row():
                with gr.Column(scale=1):
                    # 任务输入
                    task_input = gr.Textbox(
                        label="📝 任务指令",
                        placeholder="输入您的任务...",
                        lines=2
                    )
                    
                    # 快速任务
                    with gr.Column():
                        gr.Markdown("**快速任务:**")
                        quick_tasks = [
                            "打开设置", "返回主页", "打开微信", "下拉搜索"
                        ]
                        for task in quick_tasks:
                            gr.Button(task, size="sm").click(
                                lambda t=task: t, outputs=task_input
                            )
                    
                    # 控制区
                    with gr.Row():
                        start_btn = gr.Button("🚀 开始", variant="primary")
                        stop_btn = gr.Button("⏹️ 停止", variant="stop")
                    
                    # 状态区
                    status_box = gr.Textbox(label="状态", value="就绪", interactive=False)
                    progress_box = gr.Textbox(label="进度", value="0/0", interactive=False)
                
                with gr.Column(scale=1):
                    # 三张截图显示
                    with gr.Row():
                        original_screenshot = gr.Image(
                            label="📱 原生截图",
                            type="filepath",
                            height=250
                        )
                        grounded_screenshot = gr.Image(
                            label="🎯 Grounded截图",
                            type="filepath",
                            height=250
                        )
                        marked_screenshot = gr.Image(
                            label="✅ 操作标记截图",
                            type="filepath",
                            height=250
                        )
            
            # 日志区
            log_box = gr.Textbox(
                label="📋 执行日志",
                lines=8,
                interactive=False,
                autoscroll=True
            )
            
            # 创建状态变量
            state_data = gr.State({
                "status": "就绪",
                "progress": "0/0",
                "log": "",
                "screenshot": None
            })
            
            # 配置更新函数
            def update_model_config(model_name, api_base):
                """更新模型配置"""
                self.config["model_name"] = model_name
                self.config["base_url"] = api_base
                return f"✅ 模型配置已更新: {model_name}"

            def update_network_config(host_ip, serial, use_network):
                """更新网络配置"""
                self.network_config["host_ip"] = host_ip
                self.network_config["serial"] = serial
                self.network_config["use_network"] = use_network
                return f"✅ 网络配置已更新: {'网络ADB' if use_network else '本地ADB'} - {host_ip}:{serial}"

            # 绑定配置更新
            model_dropdown.change(
                fn=update_model_config,
                inputs=[model_dropdown, api_base_input],
                outputs=[]
            )

            use_network_checkbox.change(
                fn=update_network_config,
                inputs=[host_ip_input, serial_input, use_network_checkbox],
                outputs=[]
            )

            # 事件处理
            def handle_start(task, current_state):
                if self.is_running:
                    return current_state["status"], current_state["progress"], current_state["log"], current_state["screenshot"], current_state
                
                if not task.strip():
                    return "❌ 请输入任务", "0/0", "请输入有效任务", None, current_state
                
                # 启动任务
                self.stop_flag = False
                self.current_log = f"🚀 开始执行: {task}\n"
                
                # 后台执行
                thread = threading.Thread(target=self._execute_task, args=(task,))
                thread.daemon = True
                thread.start()
                
                new_state = {
                    "status": "🔄 执行中",
                    "progress": "启动中",
                    "log": self.current_log,
                    "original": None,
                    "grounded": None,
                    "marked": None
                }

                return (new_state["status"], new_state["progress"], new_state["log"],
                       new_state["original"], new_state["grounded"], new_state["marked"], new_state)
            
            def handle_stop(current_state):
                self.stop_flag = True
                if self.is_running:
                    self.current_log += "\n⏹️ 停止请求已发送\n"
                
                new_state = current_state.copy()
                new_state["status"] = "🟡 停止中"
                new_state["log"] = self.current_log

                return (new_state["status"], new_state["progress"], new_state["log"],
                       new_state.get("original"), new_state.get("grounded"), new_state.get("marked"), new_state)
            
            def update_state(current_state):
                """更新状态 - 这个函数会被定时调用"""
                if self.is_running:
                    status = f"🔄 执行中 - 第{self.agent.current_round if self.agent else 0}轮"
                    progress = f"{self.agent.current_round if self.agent else 0}/{self.agent.max_rounds if self.agent else 15}"
                else:
                    status = "🟢 就绪"
                    progress = "0/0"

                # 获取三张截图
                original_img, grounded_img, marked_img = self._get_three_screenshots()

                new_state = {
                    "status": status,
                    "progress": progress,
                    "log": self.current_log,
                    "original": original_img,
                    "grounded": grounded_img,
                    "marked": marked_img
                }

                return (new_state["status"], new_state["progress"], new_state["log"],
                       new_state["original"], new_state["grounded"], new_state["marked"], new_state)
            
            # 绑定事件
            start_btn.click(
                fn=handle_start,
                inputs=[task_input, state_data],
                outputs=[status_box, progress_box, log_box, original_screenshot, grounded_screenshot, marked_screenshot, state_data]
            )

            stop_btn.click(
                fn=handle_stop,
                inputs=[state_data],
                outputs=[status_box, progress_box, log_box, original_screenshot, grounded_screenshot, marked_screenshot, state_data]
            )
            
            # 添加可见的自动更新按钮
            with gr.Row():
                auto_update_btn = gr.Button("🔄 自动更新", size="sm")
                gr.Markdown("⚡ 点击上方按钮手动更新，或等待自动更新")

            # 手动更新功能
            auto_update_btn.click(
                fn=update_state,
                inputs=[state_data],
                outputs=[status_box, progress_box, log_box, original_screenshot, grounded_screenshot, marked_screenshot, state_data]
            )

            # 自动更新 - 使用JavaScript定时器
            interface.load(
                fn=None,
                js="""
                function() {
                    // 每3秒自动点击更新按钮
                    setInterval(function() {
                        const buttons = document.querySelectorAll('button');
                        for (let btn of buttons) {
                            if (btn.textContent.includes('🔄 自动更新')) {
                                btn.click();
                                break;
                            }
                        }
                    }, 3000);
                    return [];
                }
                """
            )
        
        return interface
    
    def _get_latest_screenshot(self):
        """获取最新截图"""
        try:
            # 从agent输出目录获取
            if self.agent and hasattr(self.agent, 'output_dir') and self.agent.output_dir:
                screenshots = glob.glob(os.path.join(self.agent.output_dir, "*_image.jpg"))
                if screenshots:
                    return max(screenshots, key=os.path.getctime)
            
            # 获取实时截图
            if self.agent and self.agent.android_device:
                temp_path = "realtime_screenshot.jpg"
                result = self.agent.android_device.screenshot(output_path=temp_path)
                if "saved" in result and os.path.exists(temp_path):
                    return temp_path
        except Exception as e:
            print(f"获取截图失败: {e}")
        
        return None

    def _get_three_screenshots(self):
        """获取三张截图：原生、grounded、marked"""
        original_img = None
        grounded_img = None
        marked_img = None

        try:
            if self.agent and hasattr(self.agent, 'output_dir') and self.agent.output_dir:
                # 获取最新轮次的截图
                current_round = self.agent.current_round if self.agent.current_round > 0 else 1

                # 原生截图
                original_path = os.path.join(self.agent.output_dir, f"round{current_round}_image.jpg")
                if os.path.exists(original_path):
                    original_img = original_path

                # Grounded截图
                grounded_path = os.path.join(self.agent.output_dir, f"round{current_round}_grounded.jpg")
                if os.path.exists(grounded_path):
                    grounded_img = grounded_path

                # Marked截图（操作标记）- 注意agent.py中的文件名格式
                marked_path = os.path.join(self.agent.output_dir, f"round{current_round}_image_marked.jpg")
                if os.path.exists(marked_path):
                    marked_img = marked_path

                # 如果没有当前轮次的，尝试获取最新的
                if not original_img:
                    screenshots = glob.glob(os.path.join(self.agent.output_dir, "*_image.jpg"))
                    if screenshots:
                        original_img = max(screenshots, key=os.path.getctime)

                if not grounded_img:
                    grounded_screenshots = glob.glob(os.path.join(self.agent.output_dir, "*_grounded.jpg"))
                    if grounded_screenshots:
                        grounded_img = max(grounded_screenshots, key=os.path.getctime)

                if not marked_img:
                    marked_screenshots = glob.glob(os.path.join(self.agent.output_dir, "*_image_marked.jpg"))
                    if marked_screenshots:
                        marked_img = max(marked_screenshots, key=os.path.getctime)

            # 如果没有agent输出，尝试获取实时截图作为原生截图
            if not original_img and self.agent and self.agent.android_device:
                temp_path = "realtime_original.jpg"
                result = self.agent.android_device.screenshot(output_path=temp_path)
                if "saved" in result and os.path.exists(temp_path):
                    original_img = temp_path

        except Exception as e:
            print(f"获取三张截图失败: {e}")

        return original_img, grounded_img, marked_img

    def _execute_task(self, task):
        """后台执行任务"""
        self.is_running = True
        
        try:
            # 初始化
            self.current_log += "🔧 初始化Agent...\n"
            
            openai_client = OpenAI(
                base_url=self.config["base_url"],
                api_key=self.config["api_key"]
            )
            
            # 根据配置选择设备类型
            if self.network_config["use_network"]:
                from network_tooluse import NetworkAndroidDevice
                android_device = NetworkAndroidDevice.getNetworkDevice(
                    host_ip=self.network_config["host_ip"],
                    serial=self.network_config["serial"]
                )
                if not android_device:
                    raise Exception(f"无法连接到网络设备 {self.network_config['host_ip']}:{self.network_config['serial']}")
            else:
                from tooluse import AndroidDevice
                android_device = AndroidDevice.getFirstDevice()
                if not android_device:
                    raise Exception("无法找到本地Android设备")

            self.agent = AppUseAgent(
                openai_client=openai_client,
                model_name=self.config["model_name"],
                output_base_dir="realtime_runs",
                max_rounds=15,
                auto_init_device=False  # 禁用自动设备初始化
            )

            # 手动设置设备
            self.agent.android_device = android_device
            
            self.current_log += "✅ 初始化完成\n"
            
            # 执行任务
            self._run_task(task)
            
        except Exception as e:
            self.current_log += f"❌ 执行出错: {str(e)}\n"
        finally:
            self.is_running = False
    
    def _run_task(self, task):
        """执行任务"""
        self.agent.current_round = 0
        status = "Ongoing"
        
        while (self.agent.current_round < self.agent.max_rounds and 
               status == "Ongoing" and 
               not self.stop_flag):
            
            self.agent.current_round += 1
            self.current_log += f"\n🔄 第{self.agent.current_round}轮:\n"
            
            try:
                # 截图
                screenshot_path = os.path.join(
                    self.agent.output_dir, 
                    f"round{self.agent.current_round}_image.jpg"
                )
                self.agent.android_device.screenshot(output_path=screenshot_path)
                self.current_log += "📸 截图完成\n"
                
                # AI分析
                from grounding import grounding
                _, prompt = grounding.grounding()
                step_data = self.agent._get_model_response_openai(
                    image_path=screenshot_path, prompt=prompt, task=task
                )
                
                status = step_data.status
                self.current_log += f"🤖 AI决策: {status}\n"
                
                if step_data.action:
                    self.current_log += f"⚡ 操作: {step_data.action}\n"
                
                # 执行操作
                if status == "Ongoing" and step_data.action:
                    from PIL import Image
                    image = Image.open(screenshot_path)
                    tool_result, _, _ = self.agent._execute_action(
                        step_data, screenshot_path, image.size, task
                    )
                    self.current_log += f"✅ 完成: {tool_result}\n"
                
                if status == "Done" and step_data.summary:
                    self.current_log += f"🎉 任务完成: {step_data.summary}\n"
                
                time.sleep(1)
                
            except Exception as e:
                self.current_log += f"❌ 第{self.agent.current_round}轮出错: {str(e)}\n"
                break
        
        if self.stop_flag:
            self.current_log += "\n⏹️ 任务已停止\n"
        else:
            self.current_log += "\n🎉 任务完成\n"

def main():
    """启动实时界面"""
    print("🚀 启动Agent VL实时界面...")
    
    ui = RealtimeAgentUI()
    interface = ui.create_interface()
    
    interface.launch(
        server_name="0.0.0.0",
        server_port=7861,  # 使用不同端口避免冲突
        share=False
    )

if __name__ == "__main__":
    main()
