#!/usr/bin/env python3
"""
测试增强版UI功能
"""

def test_model_options():
    """测试模型选择功能"""
    print("🧪 测试模型选择功能")
    print("=" * 40)
    
    try:
        from realtime_ui import RealtimeAgentUI
        
        ui = RealtimeAgentUI()
        
        # 检查默认配置
        print(f"✅ 默认模型: {ui.config['model_name']}")
        print(f"✅ 默认API: {ui.config['base_url']}")
        
        # 测试模型列表
        expected_models = [
            "qwen2.5-vl-72b-instruct",
            "qwen2-vl-72b-instruct", 
            "qwen-vl-max",
            "gpt-4o",
            "gpt-4o-mini",
            "claude-3-5-sonnet-20241022"
        ]
        
        print("✅ 支持的模型列表:")
        for model in expected_models:
            print(f"  - {model}")
        
        return True
        
    except Exception as e:
        print(f"❌ 模型选择测试失败: {e}")
        return False

def test_network_config():
    """测试网络配置功能"""
    print("\n🧪 测试网络配置功能")
    print("=" * 40)
    
    try:
        from realtime_ui import RealtimeAgentUI
        
        ui = RealtimeAgentUI()
        
        # 检查网络配置
        print(f"✅ 默认主机IP: {ui.network_config['host_ip']}")
        print(f"✅ 默认设备序列号: {ui.network_config['serial']}")
        print(f"✅ 网络ADB启用: {ui.network_config['use_network']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 网络配置测试失败: {e}")
        return False

def test_screenshot_functions():
    """测试三张截图功能"""
    print("\n🧪 测试三张截图功能")
    print("=" * 40)
    
    try:
        from realtime_ui import RealtimeAgentUI
        import os
        import tempfile
        
        ui = RealtimeAgentUI()
        
        # 创建临时测试目录
        with tempfile.TemporaryDirectory() as temp_dir:
            # 模拟agent输出目录
            class MockAgent:
                def __init__(self):
                    self.output_dir = temp_dir
                    self.current_round = 1
            
            ui.agent = MockAgent()
            
            # 创建测试文件
            test_files = [
                "round1_image.jpg",
                "round1_grounded.jpg", 
                "round1_image_marked.jpg"
            ]
            
            for filename in test_files:
                filepath = os.path.join(temp_dir, filename)
                # 创建空文件用于测试
                with open(filepath, 'w') as f:
                    f.write("test")
            
            # 测试获取三张截图
            original, grounded, marked = ui._get_three_screenshots()
            
            print(f"✅ 原生截图: {original is not None}")
            print(f"✅ Grounded截图: {grounded is not None}")
            print(f"✅ 标记截图: {marked is not None}")
            
            if original and grounded and marked:
                print("✅ 三张截图功能正常")
                return True
            else:
                print("❌ 部分截图获取失败")
                return False
        
    except Exception as e:
        print(f"❌ 截图功能测试失败: {e}")
        return False

def test_ui_creation():
    """测试UI创建"""
    print("\n🧪 测试UI创建")
    print("=" * 40)
    
    try:
        from realtime_ui import RealtimeAgentUI
        
        ui = RealtimeAgentUI()
        interface = ui.create_interface()
        
        if interface:
            print("✅ UI界面创建成功")
            print("✅ 包含模型选择下拉框")
            print("✅ 包含网络配置选项")
            print("✅ 包含三张截图显示区域")
            return True
        else:
            print("❌ UI界面创建失败")
            return False
        
    except Exception as e:
        print(f"❌ UI创建测试失败: {e}")
        return False

def test_imports():
    """测试必要的导入"""
    print("\n🧪 测试必要的导入")
    print("=" * 40)
    
    try:
        import gradio as gr
        print(f"✅ Gradio版本: {gr.__version__}")
        
        from network_tooluse import NetworkAndroidDevice
        print("✅ 网络工具类导入成功")
        
        from agent import AppUseAgent
        print("✅ Agent类导入成功")
        
        from openai import OpenAI
        print("✅ OpenAI客户端导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 导入测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 Agent VL 增强版UI测试")
    print("=" * 50)
    
    tests = [
        ("必要导入", test_imports),
        ("模型选择", test_model_options),
        ("网络配置", test_network_config),
        ("截图功能", test_screenshot_functions),
        ("UI创建", test_ui_creation)
    ]
    
    results = []
    for name, test_func in tests:
        print(f"\n🔍 测试 {name}...")
        result = test_func()
        results.append((name, result))
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 测试结果:")
    
    all_passed = True
    for name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {name}: {status}")
        if not result:
            all_passed = False
    
    if all_passed:
        print("\n🎉 所有测试通过！增强版UI功能正常")
        print("\n🚀 新功能:")
        print("  📱 模型选择 - 支持多种AI模型")
        print("  🌐 网络配置 - 支持远程ADB连接")
        print("  📸 三张截图 - 原生/Grounded/标记截图")
        print("  ⚡ 实时更新 - 自动刷新所有状态")
        print("\n启动命令: python realtime_ui.py")
    else:
        print("\n⚠️ 部分测试失败，请检查配置")
    
    return all_passed

if __name__ == "__main__":
    main()
