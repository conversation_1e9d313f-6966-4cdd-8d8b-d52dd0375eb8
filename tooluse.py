""" Android设备工具类 """
import subprocess
import os
import shlex
import logging
from typing import Optional, Tuple, List
from PIL import Image
from tempfile import mktemp
import json

# 获取ADB路径
def get_adb_path():
    """获取ADB可执行文件的路径"""
    current_dir = os.path.dirname(os.path.abspath(__file__))

    # 检查不同操作系统的ADB文件
    if os.name == 'nt':  # Windows
        adb_path = os.path.join(current_dir, 'platform-tools', 'adb.exe')
    else:  # Linux/Mac
        adb_path = os.path.join(current_dir, 'platform-tools', 'adb')
        # 如果没有找到，尝试使用系统的adb
        if not os.path.exists(adb_path):
            adb_path = '/home/<USER>/Personal_Projects/agent_vl/agent_vl/platform-tools/adb.exe'

    return adb_path

ADB_PATH = get_adb_path()

class AdbResult:
    def __init__(self, succeed: bool, output: str, error: str):
        self.succeed = succeed
        self.output = output
        self.error = error

class AndroidDevice:
    def __init__(self, serial: str, device_alias: str = "默认Android设备"):
        self.serial = serial
        self.alias = device_alias
        self.logger = logging.getLogger(__name__)

    def adb(self, command: str) -> AdbResult:
        """
        执行adb命令
        :param command: adb命令
        :return: AdbResult对象
        """
        full_command = f"{ADB_PATH} -s {self.serial} {command}"
        try:
            proc = subprocess.run(
                full_command,
                shell=True,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                timeout=30
            )
            output = proc.stdout.decode('utf-8', errors="ignore").strip()
            errors = proc.stderr.decode('utf-8', errors="ignore").strip()
            
            return AdbResult(proc.returncode == 0, output, errors)
        except subprocess.TimeoutExpired:
            return AdbResult(False, "", "Command timeout")
        except Exception as e:
            return AdbResult(False, "", str(e))

    def execute_adb_command(self, command: str) -> Tuple[int, str, str]:
        """执行adb shell命令，返回(exit_code, stdout, stderr)"""
        # 替换命令中的adb为完整路径
        if command.startswith('adb '):
            command = command.replace('adb ', f"{ADB_PATH} ", 1)

        self.logger.info(f"[AndroidDevice] Executing command: {command}")
        try:
            proc = subprocess.Popen(
                command,
                shell=True,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            stdout, stderr = proc.communicate()
            
            # 处理编码问题
            try:
                stdout_str = stdout.decode('utf-8').strip()
            except UnicodeDecodeError:
                stdout_str = stdout.decode('gbk', errors='ignore').strip()
                
            try:
                stderr_str = stderr.decode('utf-8').strip()
            except UnicodeDecodeError:
                stderr_str = stderr.decode('gbk', errors='ignore').strip()
                
            return proc.returncode, stdout_str, stderr_str
        except Exception as e:
            self.logger.error(f"[AndroidDevice] Error executing command: {e}")
            return -1, "", str(e)

    def check_device_connected(self) -> bool:
        """检查ADB设备是否连接"""
        returncode, stdout, stderr = self.execute_adb_command(f"{ADB_PATH} devices")
        if returncode == 0 and "device" in stdout and "List of devices attached" in stdout:
            lines = stdout.split('\n')
            if len(lines) > 1 and "device" in lines[1]:
                return True
        return False

    @classmethod
    def getFirstDevice(cls, alias: str = "默认Android设备") -> Optional['AndroidDevice']:
        """
        获取第一个Android设备
        :param alias: 设备别名
        :return: AndroidDevice对象或None
        """
        proc = subprocess.run(
            f"{ADB_PATH} devices",
            shell=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
        )
        if proc.returncode != 0:
            print("获取设备列表失败:", proc.stderr.decode('utf-8', errors="ignore").strip())
            return None
        
        output = proc.stdout.decode('utf-8', errors="ignore").strip()
        lines = output.splitlines()
        
        devices = []
        for line in lines[1:]:
            if line.strip() and "device" in line and "List of devices" not in line:
                serial = line.split()[0]
                devices.append(serial)
        
        if not devices:
            raise RuntimeError("没有找到Android设备，请连接设备并确保adb已安装。")
        
        return cls(devices[0], alias)

    def get_screenshot(self) -> Optional[Image.Image]:
        """
        获取设备截图,返回PIL Image对象
        """
        temp_path = mktemp(prefix="screenshot_", suffix=".png")
        
        try:
            device_temp_path = "/sdcard/screenshot_temp.png"
            ret = self.adb(f"shell screencap -p {device_temp_path}")
            
            if not ret.succeed:
                return None
            
            ret_pull = self.adb(f"pull {device_temp_path} {temp_path}")
            if not ret_pull.succeed:
                return None
            
            self.adb(f"shell rm {device_temp_path}")
            
            img = Image.open(temp_path)
            return img
            
        except Exception as e:
            return None
        finally:
            try:
                if os.path.exists(temp_path):
                    os.remove(temp_path)
            except:
                pass

    def get_ui_tree(self) -> Optional[str]:
        """
        获取UI树,返回JSON格式的字符串
        """
        temp_path = mktemp(prefix="hierarchy_", suffix=".xml")
        ret = self.adb(f"shell uiautomator dump /sdcard/window_dump.xml")
        
        if ret.succeed:
            ret_pull = self.adb(f"pull /sdcard/window_dump.xml {temp_path}")
            if ret_pull.succeed:
                try:
                    with open(temp_path, 'r', encoding='utf-8') as f:
                        xml_content = f.read()
                    
                    import xml.etree.ElementTree as ET
                    try:
                        root = ET.fromstring(xml_content)
                        ui_tree = {"hierarchy": xml_content}
                        return json.dumps(ui_tree, ensure_ascii=False)
                    except:
                        return xml_content
                except Exception as e:
                    return None
                finally:
                    try:
                        os.remove(temp_path)
                    except:
                        pass
        
        ret = self.adb("shell dumpsys window windows")
        if ret.succeed:
            window_info = {"windows": ret.output}
            return json.dumps(window_info, ensure_ascii=False)
        
        return None

    # ========== 工具方法 ==========
    
    def start_app(self, bundle_name: str, ability_name: Optional[str] = None) -> str:
        """启动指定包名的App"""
        if not self.check_device_connected():
            return "Error: No ADB device connected"
        
        if ability_name:
            command = f"adb shell am start -n {bundle_name}/{ability_name}"
        else:
            command = f"adb shell monkey -p {bundle_name} 1"
        
        returncode, stdout, stderr = self.execute_adb_command(command)
        if returncode == 0:
            return f"App {bundle_name} started successfully."
        else:
            return f"Error starting app {bundle_name}: {stderr}"

    def fling(self, start_point: Tuple[float, float], end_point: Tuple[float, float],
              swipe_velocity_pps: Optional[int] = 300) -> str:
        """模拟快滑操作"""
        if not self.check_device_connected():
            return "Error: No ADB device connected"
        
        from_x, from_y = start_point
        to_x, to_y = end_point
        command = f"adb shell input swipe {from_x} {from_y} {to_x} {to_y} {swipe_velocity_pps}"
        
        returncode, stdout, stderr = self.execute_adb_command(command)
        if returncode == 0:
            return f"Flung {start_point}->{end_point} successfully."
        else:
            return f"Error flinging {start_point}->{end_point}: {stderr}"

    def drag(self, start_point: Tuple[float, float], end_point: Tuple[float, float],
             duration: int = 1000) -> str:
        """模拟拖拽操作"""
        if not self.check_device_connected():
            return "Error: No ADB device connected"
        
        from_x, from_y = start_point
        to_x, to_y = end_point
        command = f"adb shell input swipe {from_x} {from_y} {to_x} {to_y} {duration}"
        
        returncode, stdout, stderr = self.execute_adb_command(command)
        if returncode == 0:
            return f"Dragged {start_point}->{end_point} successfully."
        else:
            return f"Error dragging {start_point}->{end_point}: {stderr}"

    def click(self, point_x: float, point_y: float) -> str:
        """在指定坐标进行点击操作"""
        if not self.check_device_connected():
            return "Error: No ADB device connected"
        
        command = f"adb shell input tap {int(point_x)} {int(point_y)}"
        returncode, stdout, stderr = self.execute_adb_command(command)
        
        if returncode == 0:
            return f"Clicked at ({point_x}, {point_y}) successfully."
        else:
            return f"Error clicking at ({point_x}, {point_y}): {stderr}"

    def long_click(self, point_x: float, point_y: float) -> str:
        """在指定坐标进行长按操作"""
        if not self.check_device_connected():
            return "Error: No ADB device connected"
        
        command = f"adb shell input swipe {int(point_x)} {int(point_y)} {int(point_x)} {int(point_y)} 1000"
        returncode, stdout, stderr = self.execute_adb_command(command)
        
        if returncode == 0:
            return f"Long Clicked at ({point_x}, {point_y}) successfully."
        else:
            return f"Error long clicking at ({point_x}, {point_y}): {stderr}"

    def get_screen_size(self) -> Tuple[int, int]:
        """获取设备屏幕尺寸"""
        returncode, stdout, stderr = self.execute_adb_command("adb shell wm size")
        if returncode == 0:
            try:
                size_str = stdout.split(": ")[1]
                width, height = map(int, size_str.split("x"))
                return width, height
            except:
                pass
        return 1080, 1920

    def swipe(self, direction: str = "left", swipe_velocity_pps: Optional[int] = 2000) -> str:
        """在指定方向进行滑动操作"""
        if not self.check_device_connected():
            return "Error: No ADB device connected"
        
        width, height = self.get_screen_size()
        
        if direction.lower() == "left":
            start_x, start_y = width * 0.8, height * 0.5
            end_x, end_y = width * 0.2, height * 0.5
        elif direction.lower() == "right":
            start_x, start_y = width * 0.2, height * 0.5
            end_x, end_y = width * 0.8, height * 0.5
        elif direction.lower() == "up":
            start_x, start_y = width * 0.5, height * 0.8
            end_x, end_y = width * 0.5, height * 0.2
        elif direction.lower() == "down":
            start_x, start_y = width * 0.5, height * 0.2
            end_x, end_y = width * 0.5, height * 0.8
        else:
            return f"Error: Invalid direction '{direction}'"
        
        command = f"adb shell input swipe {int(start_x)} {int(start_y)} {int(end_x)} {int(end_y)} {swipe_velocity_pps}"
        returncode, stdout, stderr = self.execute_adb_command(command)
        
        if returncode == 0:
            return f"Swiped {direction} successfully."
        else:
            return f"Error swiping {direction}: {stderr}"

    def screenshot(self, output_path: str = "screenshot.png") -> str:
        """截取设备当前屏幕"""
        if not self.check_device_connected():
            return "Error: No ADB device connected"
        
        device_temp = "/sdcard/temp_screen.png"
        
        ret = self.execute_adb_command(f"adb shell screencap -p {device_temp}")
        if ret[0] != 0:
            return f"Failed to capture screenshot: {ret[2]}"
        
        ret = self.execute_adb_command(f"adb pull {device_temp} {output_path}")
        if ret[0] != 0:
            return f"Failed to pull screenshot: {ret[2]}"
        
        self.execute_adb_command(f"adb shell rm {device_temp}")
        
        if os.path.exists(output_path):
            return f"Screenshot saved to {output_path}"
        else:
            return "Error: Screenshot file not created"

    def go_back(self) -> str:
        """返回上一级操作"""
        if not self.check_device_connected():
            return "Error: No ADB device connected"
        
        command = "adb shell input keyevent KEYCODE_BACK"
        returncode, stdout, stderr = self.execute_adb_command(command)
        
        if returncode == 0:
            return "Back key event sent successfully."
        else:
            return f"Error sending Back key event: {stderr}"

    def go_home(self) -> str:
        """返回主页面操作"""
        if not self.check_device_connected():
            return "Error: No ADB device connected"
        
        command = "adb shell input keyevent KEYCODE_HOME"
        returncode, stdout, stderr = self.execute_adb_command(command)
        
        if returncode == 0:
            return "Home key event sent successfully."
        else:
            return f"Error sending Home key event: {stderr}"

    def input_text(self, x: float, y: float, text: str) -> str:
        """在指定坐标输入文字"""
        if not self.check_device_connected():
            return "Error: No ADB device connected"
        
        click_result = self.click(x, y)
        if "Error" in click_result:
            return f"Error activating input field: {click_result}"
        
        text_quoted = shlex.quote(text)
        command = f"adb shell input text {text_quoted}"
        returncode, stdout, stderr = self.execute_adb_command(command)
        
        if returncode == 0:
            return f"Input text '{text}' at ({x}, {y}) successfully."
        else:
            return f"Error inputting text at ({x}, {y}): {stderr}"
