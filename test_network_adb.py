#!/usr/bin/env python3
"""
测试网络ADB连接
"""

import subprocess
import os

def test_network_adb():
    """测试网络ADB连接"""
    print("🧪 测试网络ADB连接")
    print("=" * 40)
    
    # 配置
    host_ip = "*************"
    serial = "emulator-5554"
    
    # 获取ADB路径
    current_dir = os.path.dirname(os.path.abspath(__file__))
    if os.name == 'nt':
        adb_path = os.path.join(current_dir, 'platform-tools', 'adb.exe')
    else:
        adb_path = os.path.join(current_dir, 'platform-tools', 'adb')
        if not os.path.exists(adb_path):
            adb_path = 'adb'  # 使用系统PATH中的adb
    
    print(f"📍 ADB路径: {adb_path}")
    print(f"🌐 目标主机: {host_ip}")
    print(f"📱 设备序列号: {serial}")
    print()
    
    # 测试1: 检查ADB是否可用
    print("🔍 测试1: 检查ADB是否可用")
    try:
        result = subprocess.run([adb_path, "version"], capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("✅ ADB可用")
            print(f"版本信息: {result.stdout.split()[4]}")
        else:
            print("❌ ADB不可用")
            print(f"错误: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ ADB测试失败: {e}")
        return False
    
    print()
    
    # 测试2: 检查网络设备连接
    print("🔍 测试2: 检查网络设备连接")
    try:
        command = [adb_path, "-H", host_ip, "devices"]
        result = subprocess.run(command, capture_output=True, text=True, timeout=10)
        
        print(f"命令: {' '.join(command)}")
        print(f"返回码: {result.returncode}")
        print(f"输出: {result.stdout}")
        
        if result.returncode == 0 and serial in result.stdout and "device" in result.stdout:
            print("✅ 网络设备连接成功")
        else:
            print("❌ 网络设备连接失败")
            if result.stderr:
                print(f"错误: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ 网络连接测试失败: {e}")
        return False
    
    print()
    
    # 测试3: 测试截图功能
    print("🔍 测试3: 测试截图功能")
    try:
        # 使用您提供的命令格式
        command = f"{adb_path} -H {host_ip} -s {serial} exec-out screencap -p > test_screen.png"
        result = subprocess.run(command, shell=True, capture_output=True, text=True, timeout=15)
        
        print(f"命令: {command}")
        print(f"返回码: {result.returncode}")
        
        if result.returncode == 0 and os.path.exists("test_screen.png"):
            file_size = os.path.getsize("test_screen.png")
            print(f"✅ 截图成功，文件大小: {file_size} bytes")
            
            # 清理测试文件
            try:
                os.remove("test_screen.png")
                print("🗑️ 测试文件已清理")
            except:
                pass
        else:
            print("❌ 截图失败")
            if result.stderr:
                print(f"错误: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ 截图测试失败: {e}")
        return False
    
    print()
    
    # 测试4: 测试基本shell命令
    print("🔍 测试4: 测试基本shell命令")
    try:
        command = [adb_path, "-H", host_ip, "-s", serial, "shell", "echo", "hello"]
        result = subprocess.run(command, capture_output=True, text=True, timeout=10)
        
        print(f"命令: {' '.join(command)}")
        print(f"返回码: {result.returncode}")
        print(f"输出: {result.stdout.strip()}")
        
        if result.returncode == 0 and "hello" in result.stdout:
            print("✅ Shell命令执行成功")
        else:
            print("❌ Shell命令执行失败")
            if result.stderr:
                print(f"错误: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ Shell命令测试失败: {e}")
        return False
    
    print()
    print("🎉 所有测试通过！网络ADB连接正常")
    return True

def test_network_tooluse():
    """测试网络工具类"""
    print("\n🧪 测试网络工具类")
    print("=" * 40)
    
    try:
        from network_tooluse import NetworkAndroidDevice
        
        # 创建网络设备
        device = NetworkAndroidDevice.getNetworkDevice(
            host_ip="*************",
            serial="emulator-5554"
        )
        
        if device:
            print("✅ 网络设备创建成功")
            
            # 测试连接
            if device.check_device_connected():
                print("✅ 设备连接检查通过")
                
                # 测试截图
                result = device.screenshot("test_network_screenshot.png")
                print(f"📸 截图测试: {result}")
                
                # 清理
                if os.path.exists("test_network_screenshot.png"):
                    os.remove("test_network_screenshot.png")
                
                return True
            else:
                print("❌ 设备连接检查失败")
                return False
        else:
            print("❌ 网络设备创建失败")
            return False
            
    except Exception as e:
        print(f"❌ 网络工具类测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 Agent VL 网络ADB测试")
    print("=" * 50)
    
    # 基础ADB测试
    if not test_network_adb():
        print("\n❌ 基础ADB测试失败，请检查网络连接和配置")
        return
    
    # 工具类测试
    if not test_network_tooluse():
        print("\n❌ 网络工具类测试失败")
        return
    
    print("\n🎉 所有测试通过！")
    print("现在可以使用网络ADB功能了")
    print("\n📝 使用方法:")
    print("1. 确保PC上的Android模拟器正在运行")
    print("2. 确保开发板和PC在同一局域网")
    print("3. 运行: python realtime_ui.py")
    print("4. 在界面中配置正确的IP和端口")

if __name__ == "__main__":
    main()
