#!/usr/bin/env python3
"""
Agent VL 快速启动脚本
"""

import subprocess
import sys
import os

def main():
    """快速启动Agent VL实时界面"""
    print("🚀 Agent VL 快速启动")
    print("=" * 40)
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        print("❌ 需要Python 3.8或更高版本")
        return
    
    print(f"✅ Python版本: {sys.version}")
    
    # 检查Gradio
    try:
        import gradio as gr
        print(f"✅ Gradio版本: {gr.__version__}")
    except ImportError:
        print("📦 正在安装Gradio...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "gradio"])
        print("✅ Gradio安装完成")
    
    # 启动界面
    print("\n🌐 启动实时界面...")
    print("界面地址: http://localhost:7861")
    print("按 Ctrl+C 停止服务")
    print("-" * 40)
    
    try:
        # 直接运行realtime_ui.py
        subprocess.run([sys.executable, "realtime_ui.py"])
    except KeyboardInterrupt:
        print("\n👋 界面已关闭")
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        print("请检查依赖是否正确安装")

if __name__ == "__main__":
    main()
