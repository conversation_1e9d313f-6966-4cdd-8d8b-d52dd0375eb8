import sys
from PIL import Image, ImageDraw

def mark_coordinate(image_path, x, y, circle_radius=20, output_path=None):
    """
    Mark a coordinate on an image with a circle and save the result.

    Args:
        image_path (str): Path to the input image
        x (int): X coordinate to mark
        y (int): Y coordinate to mark
        circle_radius (int): Radius of the circle (default 20)
        output_path (str): Optional output path. If not provided, will be generated.

    Returns:
        str: Path to the saved output image
    """
    try:
        # Open image
        img = Image.open(image_path)
        
        # 转换图像模式为RGB（如果当前是RGBA）
        if img.mode == 'RGBA':
            img = img.convert('RGB')

        # Create drawing context
        draw = ImageDraw.Draw(img)

        # Draw circle at specified coordinates
        circle_bbox = [
            (x - circle_radius, y - circle_radius),
            (x + circle_radius, y + circle_radius)
        ]
        draw.ellipse(circle_bbox, fill="red", outline="white")

        # Generate output path if not provided
        if output_path is None:
            if '.' in image_path:
                parts = image_path.rsplit('.', 1)
                output_path = f"{parts[0]}_marked.{parts[1]}"
            else:
                output_path = f"{image_path}_marked.jpg"

        # Save output
        img.save(output_path)
        return output_path

    except Exception as e:
        raise RuntimeError(f"Error processing image: {str(e)}")



def mark_coordinates(image_path, coordinates, circle_radius=20, output_path=None):
    """
    Mark multiple coordinates on an image with circles and save the result.

    Args:
        image_path (str): Path to the input image
        coordinates (list[tuple[int, int]]): List of (x, y) coordinates to mark
        circle_radius (int): Radius of the circle (default 20)
        output_path (str): Optional output path. If not provided, will be generated.

    Returns:
        str: Path to the saved output image
    """
    try:
        # Open image
        img = Image.open(image_path)
        # 转换图像模式为RGB（如果当前是RGBA）
        if img.mode == 'RGBA':
            img = img.convert('RGB')
        draw = ImageDraw.Draw(img)

        # Draw circles at each coordinate
        for x, y in coordinates:
            circle_bbox = [
                (x - circle_radius, y - circle_radius),
                (x + circle_radius, y + circle_radius)
            ]
            draw.ellipse(circle_bbox, fill="red", outline="white")

        # Generate output path if not provided
        if output_path is None:
            if '.' in image_path:
                parts = image_path.rsplit('.', 1)
                output_path = f"{parts[0]}_marked.{parts[1]}"
            else:
                output_path = f"{image_path}_marked.jpg"

        img.save(output_path)
        return output_path

    except Exception as e:
        raise RuntimeError(f"Error processing image: {str(e)}")


def main():
    if len(sys.argv) != 4:
        print("Usage: python show_coordinate.py <image_path> <x> <y>")
        sys.exit(1)

    image_path = sys.argv[1]
    try:
        x = int(sys.argv[2])
        y = int(sys.argv[3])
    except ValueError:
        print("Error: x and y must be integers")
        sys.exit(1)

    try:
        output_path = mark_coordinate(image_path, x, y)
        print(f"Image saved as {output_path}")
    except Exception as e:
        print(str(e))
        sys.exit(1)


if __name__ == "__main__":
    main()
