import os
import datetime
import json
import logging
import time
import base64
import ast
import re
from PIL import Image
from pydantic import BaseModel, Field
from typing import Literal, Optional
from grounding import grounding 
from google.genai import types
from openai import OpenAI
from show_coordinate import mark_coordinate, mark_coordinates
from tooluse import AndroidDevice  
from typing import Union

# Configure logging
logging.basicConfig(
    level=logging.CRITICAL,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)

class Step(BaseModel):
    status: Literal["Done", "Ongoing"] = Field(..., description="当前操作的状态。Done表示完成了用户的指令，Ongoing表示还未完成用户的指令，需要进行下一步操作")
    action: Optional[Literal["Click", "Swipe", "GoBack", "GoHome", "InputText", "Fling", "Drag", "LongClick"]] = Field(None, description="当前操作的类型")
    target_element_description: Optional[str] = Field(None, description="A clear description of the UI element to interact with, e.g., 'the blue login button with text Sign In'.")
    direction: Optional[Literal["left", "right", "up", "down"]] = Field(None, description="The direction for a swipe action")
    text: Optional[str] = Field(None, description="The text to input for InputText action")
    summary: Optional[str] = Field(None, description="当任务完成时（status为Done）对任务完成情况的总结，包括用户指令的实现情况和最终结果")

class AppUseAgent:
    def __init__(self,
                 openai_client: OpenAI,
                 output_base_dir: str = "agent_output",
                 max_rounds: int = 30,
                 model_name: str = None,
                 history_limit: int = 3):
        self.openai_client = openai_client
        self.output_base_dir = output_base_dir
        self.max_rounds = max_rounds
        self.model_name = model_name
        self.history_limit = history_limit
        self.history = []
        self.error_ids = set()
        self.last_clicked_id = None
        self.current_round = 0
        self.output_dir = self._setup_output_directory()
        self.log_file_path = os.path.join(self.output_dir, "operation_log.txt")
        self.prompt_path = os.path.join(self.output_dir, "prompt.txt")
        
        # 初始化Android设备
        self.android_device = AndroidDevice.getFirstDevice()
        
        self._initialize_log_file()

    def _setup_output_directory(self) -> str:
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        output_dir = os.path.join(self.output_base_dir, f"run_{timestamp}")
        os.makedirs(output_dir, exist_ok=True)
        return output_dir

    def _initialize_log_file(self):
        header1 = "任务指令: (未设置)\n"
        header2 = "轮次\t状态\t操作\t目标元素\t工具结果\t总结\n"
        
        with open(self.log_file_path, 'w') as f:
            f.write(header1)
            f.write(header2)
        print(header1, end='')
        print(header2, end='')

    def _log_operation(self, record: dict):
        log_line = f"{record['round']}\t{record['status']}\t{record.get('action', '')}\t"
        log_line += f"{record.get('target_element_description', '')}\t{str(record.get('tool_result', ''))}\t"
        log_line += f"{record.get('summary', '')}\n"
        
        with open(self.log_file_path, 'a') as f:
            f.write(log_line)
        print(log_line, end='')

    def encode_image(self, image_path):
        with open(image_path, 'rb') as image_file:
            return base64.b64encode(image_file.read()).decode('utf-8')

    def _log_prompt(self, record: dict, prompt: Union[dict, str]):
        try:
            if isinstance(prompt, dict):
                prompt_str = json.dumps(prompt, ensure_ascii=False, indent=4)
            elif isinstance(prompt, str):
                try:
                    prompt_dict = json.loads(prompt)
                    prompt_str = json.dumps(prompt_dict, ensure_ascii=False, indent=4)
                except json.JSONDecodeError:
                    prompt_str = prompt
            else:
                prompt_str = str(prompt)
            
            prompt_line = f"{record['round']}\t{record['status']}\tPrompt:\n{prompt_str}\n"
            
            with open(self.prompt_path, 'a', encoding='utf-8') as f:
                f.write(prompt_line)
                
        except Exception as e:
            print(f"记录prompt失败: {str(e)}")
            with open(self.prompt_path, 'a', encoding='utf-8') as f:
                f.write(f"{record['round']}\tERROR LOGGING PROMPT: {str(e)}\n")

    def _get_model_response_openai(self, image_path: str, prompt: str, task: str) -> Step:
        history_text = "历史操作记录:\n"
        for i, record in enumerate(self.history[-self.history_limit:], 1):
            history_text += f"第{i}轮: {record}\n"

        SYSTEM_PROMPT = """你是一个严谨的App操作助手。你的任务是根据用户的指令和当前屏幕截图，准确地判断任务状态并决定下一步操作。

请严格遵循以下思考步骤：
1.  **分析用户指令**：明确用户的最终目标是什么。例如，指令"关闭蓝牙"，目标就是让蓝牙处于关闭状态。
2.  **分析当前屏幕截图**：仔细观察屏幕上与指令相关的UI元素的状态。当前所有可交互的UI元素都已经被边框（grounding box）给框起来了，但是你通常每一步只需要选择其中的一个或很少数的几个进行交互。
    - **特别注意**：对于开关（toggle switch）这类元素，灰色或熄灭状态通常代表【关闭】，而蓝色、绿色等高亮颜色通常代表【开启】。
3.  **比较并决策**：
    - 将截图中的当前状态与用户的目标进行比较。
    - 如果当前状态**已经满足**了用户的目标（例如，指令是"关闭蓝牙"，而截图里蓝牙开关已经是灰色关闭状态），则任务已完成。此时必须返回 `status: "Done"`，并且不提供任何 `action`。
    - 如果当前状态**未满足**用户的目标（例如，指令是"关闭蓝牙"，而截图里蓝牙开关是蓝色开启状态），则任务需要继续。此时应返回 `status: "Ongoing"`，并提供为达成目标所需的下一步 `action`。

> **4. 滑动操作选择指南 (Action Selection Guide for Swipes):**
> **当你决定下一步的操作是滑动时，必须从 `Drag`, `Fling`, `Swipe` 中选择最恰当的一个。请严格遵循以下规则：**
> 
> *   **规则1：判断是否为 `Drag` (拖拽)**
>     *   **场景**: 任务是否需要精确地抓住并移动一个**控件的“把手”**？
>     *   **例子**: 调整音量/亮度**滑块**、在地图上拖动图钉、拉动进度条。
>     *   **决策**: 如果是，必须选择 **`Drag`**。
> 
> *   **规则2：判断是否为 `Fling` (快滑)**
>     *   **场景**: 如果不是`Drag`，任务是否是在一个**可滚动的内容区域**（如列表、页面、滚轮）上滑动，目的是为了**浏览更多内容**？
>     *   **例子**: 滚动新闻列表、在年份选择器中选择年份、滑动切换应用页面。
>     *   **决策**: 如果是，必须选择 **`Fling`**。
> 
> *   **规则3：判断是否为 `Swipe` (滑动)**
>     *   **场景**: 如果既不是`Drag`也不是`Fling`，任务是否是一个**不依赖具体元素的通用方向性手势**？
>     *   **例子**: 从屏幕顶部**下拉打开通知栏**、在主屏幕上**向左/右滑动切换桌面**、从屏幕边缘向内滑动以返回。
>     *   **决策**: 如果是，则选择 **`Swipe`**。
> 
> ***总结：`Drag`用于控件，`Fling`用于内容，`Swipe`用于全局方向。***

5.  **重要提示**：请你不要完全相信历史操作记录的信息。你必须根据当前屏幕截图和用户指令来判断任务状态。

6.  **任务总结**：当你判断任务已经完成（status为"Done"）时，必须提供一个详细的summary字段，对任务完成情况进行总结。这个总结应该：
    - 基于用户的原始指令和当前屏幕截图
    - 描述任务是如何被完成的
    - 包含最终结果的关键信息（例如，找到了什么新闻、设置了什么状态等）
    - 使用清晰简洁的语言

7.  **操作技巧**：
    - 要打开某个app的时候，可以在任何桌面上滑操作，然后进行搜索。
    - 通过左右滑动，可以滑动到其它桌面。
    
8.  注意，我提供的屏幕截图已经包含了所有可交互的UI元素的边界框（grounding box）。
    这些边界框标记了相应序号，你可以通过序号找到我给的UI元素的具体bbox位置。
    当你进行决策时，选择一个或少数几个边界框进行交互即可。你的选择操作是选择一个序号的bbox，参考其文本描述选择像素位置，注意你要参考我给出的bbox的文本描述。
    我给出的信息就是包含了所有可交互的UI元素的边界框的截图以及其对应的文本描述，其中bbox描述格式如下：
	{
		"id": 1,
		"bbox": [15,16][17,18],
		"interactive": "clickable"
		"type": "Button",
		"content": "内容文本"，
	},
    其中bbox是一个二维数组，表示左上角和右下角的坐标，interactive表示是否可交互，type表示元素类型，content表示内容文本。
    请注意，边界框的颜色可能会有所不同，但这不影响你的决策。同时你不必一定要使用边界框的中心点进行操作，你可以根据实际情况选择合适的像素位置进行点击或滑动。
    同时如果当前所有的边界框都不满足用户的指令，你可以选择除了边界框之外的像素位置进行操作。
    
请根据以上决策，生成JSON输出。

你必须返回一个合法的 JSON 对象，不要加 Markdown 代码块，字段如下：
status：必须是 "Done" 或 "Ongoing"。
action：必须是以下之一（没有别的值）：
"Click", "Swipe", "GoBack", "GoHome", "InputText", "Fling", "Drag", "LongClick"。
target_element_description：字符串，描述要操作的元素（如："蓝色登录按钮"），无操作时可省略或设为 null。
direction：必须是 "left", "right", "up", "down" 之一，仅当 action 是 Swipe/Fling/Drag 时填写。
text：字符串，仅当 action 是 InputText 时填写。
summary：字符串，仅当 status 为 Done 时填写，总结任务完成情况。
"""

        base64_image = self.encode_image(image_path)

        # 合并所有文本内容
        combined_text = f"{history_text}\n\n{prompt}\n\n{task}"

        # 按照阿里云DashScope的正确格式：图片在前，文本在后
        messages = [
            {"role": "system", "content": SYSTEM_PROMPT},
            {
                "role": "user",
                "content": [
                    {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{base64_image}"}},
                    {"type": "text", "text": combined_text}
                ]
            }
        ]


        try:
            response = self.openai_client.chat.completions.create(
                model=self.model_name,
                messages=messages,
                max_tokens=2048,
                temperature=0.1
            )
        except Exception as e:
            print(f"API调用失败，尝试不带系统提示: {e}")
            # 尝试不带系统提示的格式
            simplified_messages = [
                {
                    "role": "user",
                    "content": [
                        {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{base64_image}"}},
                        {"type": "text", "text": f"{SYSTEM_PROMPT}\n\n{combined_text}"}
                    ]
                }
            ]
            response = self.openai_client.chat.completions.create(
                model=self.model_name,
                messages=simplified_messages,
                max_tokens=2048
            )

        # 提取内容
        raw_content = response.choices[0].message.content

        print("raw_content: ", raw_content)

        # 清理 Markdown 包裹
        json_str = re.sub(r'```json\s*|\s*```', '', raw_content).strip()

        # 解析 JSON 并验证
        try:
            parsed = Step.model_validate_json(json_str)
        except Exception as e:
            print("JSON解析失败:", e)
            print("原始内容:", raw_content)
            raise

        return parsed


        #logging.debug(f"Response text: {response.choices[0].message}")
        #return response.choices[0].message.parsed
    
    def _get_bounding_box_openai(self, image_path: str, description: str) -> dict:
        prompt_bbox = (f"Detect {description} in the image. "
                       "the answer should be a json dict as {\"box_2d\": [ymin, xmin, ymax, xmax]}，the key shoule be box_2d."
                       "The box_2d should be [ymin, xmin, ymax, xmax] normalized to 0-1000.")

        base64_image = self.encode_image(image_path)

        messages = []
        user_content = []
        user_content.append({"type": "text", "text": prompt_bbox})
        user_content.append({"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{base64_image}"}})
        messages.append({"role": "user", "content": user_content})

        response_bbox = self.openai_client.chat.completions.create(
            model = self.model_name,
            messages=messages,
            response_format={"type": "json_object"}
        )

        #logging.debug(f"Bounding box response: {response_bbox.choices[0].message}")
        return json.loads(response_bbox.choices[0].message.content)

    def _get_key_points(self, image_path: str, task: str, ui_description: str,
                        reference_action: Optional[Literal["Fling", "Drag"]]) -> dict:
        """
        Generates start and end points for a specified Fling, Drag action.

        image: A current screenshot of the phone's screen.
        task: The user's high-level goal, e.g., 'scroll down', 'set volume to 80%'.
        ui_description: A precise description of the UI element to act upon, generated by a previous analysis.
        reference_action: The specific action to perform, either "Fling" or "Drag".
        """

        prompt_points = f"""
    You are a precise AI assistant for UI automation. Your role is to calculate the exact coordinates for a pre-determined gesture.

    You have been given a specific action to perform: **{reference_action}**.

    Follow these precise instructions based on the action type:

    - If the action is **"Drag"**:
      1.  Locate the element described as: "{ui_description}".
      2.  Identify its movable handle (e.g., the thumb of a slider, a toggle switch).
      3.  The `start_point` **must be the center of this handle**.
      4.  The `end_point` should be the target position on the element's track that accomplishes the task: "{task}".

    - If the action is **"Fling"**:
      1.  Locate the scrollable area described as: "{ui_description}".
      2.  The `start_point` should be a suitable point within this area to initiate a swipe.
      3.  The `end_point` should be chosen to create a swipe path that accomplishes the task: "{task}". For example, to scroll down, swipe from bottom to top.

    ---
    **Input Summary:**
    - **Action to Perform**: {reference_action}
    - **Target Element Description**: "{ui_description}"
    - **User's Goal**: "{task}"

    ---
    **Required Output Format:**
    You must provide a JSON dictionary with the exact structure: {{"start_point": [y, x], "end_point": [y, x]}}.
    The coordinates are [y, x], normalized to a 0-1000 scale. Provide only the JSON object in your response.
    """
        base64_image = self.encode_image(image_path)

        messages = []
        user_content = []
        user_content.append({"type": "text", "text": prompt_points})
        user_content.append({"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{base64_image}"}})
        messages.append({"role": "user", "content": user_content})

        # config_json = types.GenerateContentConfig(
        #     response_mime_type="application/json",
        #     temperature=0.1  # 任务极其明确，几乎不需要创造性，使用非常低的温度以保证结果的确定性。
        # )
        #
        response = self.openai_client.chat.completions.create(
            model=self.model_name,  # 如 "qwen/qwen-vl-plus"
            messages=messages,
            temperature=0.1,
            max_tokens=512,
            response_format={"type": "json_object"}  # 强制返回 JSON
        )

        try:
            return json.loads(response.choices[0].message.content)
        except Exception as e:
            logging.error(f"JSON decode error: {e}\n{response.choices[0].message.content}")
            return {}

    def _execute_action(self, step_data: Step, image_path: str, image_size: tuple, task: str | None = None) -> str:
        tool_result = "未知操作类型"

        image_with_boxes, elems_raw = grounding.grounding()
        image_path_grounded = os.path.join(self.output_dir, f"round{self.current_round}_grounded.jpg")
        image_with_boxes.save(image_path_grounded)

        # 解析和处理元素数据
        if isinstance(elems_raw, str):
            try:
                elements_obj = ast.literal_eval(elems_raw)
            except Exception as e:
                raise RuntimeError(f"无法用 ast.literal_eval 解析 elems_raw: {e}")
        else:
            elements_obj = elems_raw

        if isinstance(elements_obj, dict):
            elements = list(elements_obj.values())
        elif isinstance(elements_obj, list):
            elements = elements_obj
        else:
            raise RuntimeError(f"elements 既不是 list 也不是 dict，type={type(elements_obj)}")

        filtered = []
        for idx, elem in enumerate(elements):
            if isinstance(elem, dict) and "id" in elem and "bbox" in elem:
                filtered.append(elem)
            else:
                logging.warning(f"跳过元素 #{idx}，类型 {type(elem)}，内容 {elem}")
        if not filtered:
            raise RuntimeError("没有找到任何合法的 UI 元素 (dict 且包含 id 和 bbox)")
        elements = filtered

        centerX, centerY = None, None
        elements = [elem for elem in elements if elem.get("id") not in self.error_ids]
        if not elements:
            raise RuntimeError(f"所有可交互元素都已标记为错误，无法继续操作。错误ID列表: {self.error_ids}")

        if step_data.action in ["Click", "InputText", "LongClick"]:
            elems_info = []
            #valid_ids = set()
            valid_ids = set(elem["id"] for elem in elements)

            for elem in elements:
                eid = elem["id"]
                valid_ids.add(eid)
                desc = elem.get("describe", "").replace("\n", "")
                t = elem.get("type", "")
                (x1, y1), (x2, y2) = elem["bbox"]
                elems_info.append(f"{eid}: [{x1},{y1}]-[{x2},{y2}], 类型={t}, 描述=\"{desc}\"")

            elems_text = "\n".join(elems_info)

            prompt = (
                f"任务：\"{step_data.target_element_description}\"。\n"
                "你正在操作一个手机界面。\n"
                "以下是截图中所有可交互元素的编号、位置、类型和描述：\n"
                f"{elems_text}\n\n"
                "请根据截图和上述信息，**仅返回一个最符合任务目标的元素编号（纯数字）**。\n"
                "## 特别说明：\n"
                "- 元素的编号在可交互元素边界框的正上方，一定要选择可交互元素上方的编号\n"
                "- 如果任务是“搜索”，请**仅选择输入框类型的元素**，不要选择扫描按钮、相机按钮或搜索图标。\n"
                "- 输入框通常是一个矩形文本区域，旁边可能有“搜索”字样或放大镜图标，但**不包含相机图标或扫描图标**。\n"
                "- 如果多个元素看起来都像搜索框，请根据我提供的边界框信息，计算框的面积并选择面积最大的那个。\n"
            )

            base64_image = self.encode_image(image_path_grounded)
            #base64_image = self.encode_image(image_path)

            resp = self.openai_client.chat.completions.create(
                model=self.model_name,
                messages=[{"role": "user", "content": [
                    {"type": "image_url",
                     "image_url": {"url": f"data:image/jpeg;base64,{base64_image}"}},
                    {"type": "text", "text": prompt},
                ]}]
            )

            # 兼容多种 resp 类型：dict、OpenAI Response、Gemini Response
            if isinstance(resp, dict):
                # OpenAI 可能直接返回了 {"id": "29"}
                id_str = resp.get("id", "")
            elif hasattr(resp, "choices"):
                # OpenAI SDK 返回的 ChatCompletionResponse
                id_str = resp.choices[0].message.content
            elif hasattr(resp, "candidates"):
                # Gemini generate_content 的返回
                # .candidates 是一个列表，每个 candidate.content.parts 也是列表
                id_str = resp.candidates[0].content.parts[0].text
            elif hasattr(resp, "text"):
                id_str = resp.text
            else:
                id_str = str(resp)

            # 提取 target id（鲁棒处理 markdown、HTML、自然语言等格式）
            match = re.search(r"target\s+id\s+is\s+\*{0,2}(\d+)\*{0,2}", id_str, re.IGNORECASE)
            if match:
                target_id = int(match.group(1))  # 提取纯数字
            else:
                # fallback：尝试匹配第一个出现的数字
                fallback_match = re.search(r"\d+", id_str)
                target_id = int(fallback_match.group(0)) if fallback_match else None

            print("target_id: ", target_id)

            target_id = str(target_id)
            # 记录最后点击的 ID
            self.last_clicked_id = target_id

            # 从 elements 中找到该 id 对应的 bbox
            selected_bbox = None
            for elem in elements:
                if str(elem["id"]) == target_id:
                    selected_bbox = elem["bbox"]
                    break

            if selected_bbox is None:
                raise RuntimeError(f"找不到 id={target_id} 的元素！")
            else:
                print(f"找到目标元素的 bbox：{selected_bbox}")

            (x1, y1), (x2, y2) = selected_bbox
            centerX = (x1 + x2) // 2
            centerY = (y1 + y2) // 2
            print("centerX:", centerX, "centerY:", centerY)
            #logging.debug(f"选中元素 {target_id} 的中心点：({centerX},{centerY})")

            marked_image_filename = f"round{self.current_round}_image_marked.jpg"
            marked_image_path = os.path.join(self.output_dir, marked_image_filename)
            mark_coordinate(image_path, centerX, centerY, circle_radius=20, output_path=marked_image_path)
        elif step_data.action in ["Fling", "Drag"]:
            key_points = self._get_key_points(image_path, task, step_data.target_element_description, step_data.action)
            width, height = image_size
            y_sP = int(key_points["start_point"][0] / 1000 * height)
            x_sP = int(key_points["start_point"][1] / 1000 * width)
            y_eP = int(key_points["end_point"][0] / 1000 * height)
            x_eP = int(key_points["end_point"][1] / 1000 * width)

            #logging.debug(f"sP:{(x_sP, y_sP)}, eP:{(x_eP, y_eP)}")
            #logging.debug(f"Image size: {width}, {height}")

            marked_image_filename = f"round{self.current_round + 1}_image_marked.jpg"
            marked_image_path = os.path.join(self.output_dir, marked_image_filename)
            mark_coordinates(image_path, [(x_sP, y_sP), (x_eP, y_eP)], circle_radius=20, output_path=marked_image_path)
        else:
            logging.info(f"不需要bounding box的操作: {step_data.action}")

        try:
            if step_data.action == "Click":
                tool_result = self.android_device.click(centerX, centerY)
                logging.info(f"成功点击坐标 ({centerX}, {centerY})")
            elif step_data.action == "LongClick":
                tool_result = self.android_device.long_click(centerX, centerY)
                logging.info(f"成功点击坐标 ({centerX}, {centerY})")
            elif step_data.action == "Swipe":
                tool_result = self.android_device.swipe(direction=step_data.direction)
                logging.info(f"成功滑动: {step_data.direction}")
            elif step_data.action == "GoBack":
                print("last_clicked_id: ", self.last_clicked_id)
                if self.last_clicked_id is not None:
                    self.error_ids.add(self.last_clicked_id)
                    logging.info(f"标记错误点击 ID: {self.last_clicked_id}")
                tool_result = self.android_device.go_back()
                logging.info("成功返回上一级")
            elif step_data.action == "GoHome":
                tool_result = self.android_device.go_home()
                logging.info("成功返回主页")
            elif step_data.action == "InputText":
                tool_result = self.android_device.input_text(centerX, centerY, step_data.text)
                logging.info(f"成功输入文本: {step_data.text}")
            elif step_data.action == "Fling":
                sP, eP = (x_sP, y_sP), (x_eP, y_eP)
                tool_result = self.android_device.fling(sP, eP, 300)
                logging.info(f"成功快滑: {sP}->{eP}")
            elif step_data.action == "Drag":
                sP, eP = (x_sP, y_sP), (x_eP, y_eP)
                tool_result = self.android_device.drag(sP, eP, 600)
                logging.info(f"成功拖动: {sP}->{eP}")
            else:
                logging.error(f"未知操作类型: {step_data.action}")
        except Exception as e:
            logging.error(f"操作失败: {e}")
            tool_result = str(e)

        if step_data.action not in ["Fling", "Drag"]:
            return tool_result, centerX, centerY
        else:
            return tool_result, sP, eP

    def run(self, task: str):
        task_line = f"任务指令: {task}\n"
        
        with open(self.log_file_path, 'r+') as f:
            content = f.read()
            f.seek(0)
            f.write(task_line)
            if '\n' in content:
                header_line = content.split('\n', 1)[1]
                f.write(header_line)
            else:
                f.write(content)
                
        print(task_line, end='')

        status = "Ongoing"
        while self.current_round < self.max_rounds and status == "Ongoing":
            self.current_round += 1
            logging.info(f"===== Round {self.current_round}/{self.max_rounds} ====")

            image_filename = f"round{self.current_round}_image.jpg"
            screenshot_path = os.path.join(self.output_dir, image_filename)
            
            # 使用设备的screenshot方法
            self.android_device.screenshot(output_path=screenshot_path)
            
            image = Image.open(screenshot_path)
            image_grounded, prompt = grounding.grounding()
            step_data = self._get_model_response_openai(image_path=screenshot_path, prompt=prompt, task=task)
            logging.info(f"Step data: {step_data.target_element_description}")
            status = step_data.status

            tool_result = None
            centerX, centerY = None, None
            start_point, end_point = None, None

            if status == "Ongoing":
                if step_data.action not in ["Fling", "Drag"]:
                    tool_result, centerX, centerY = self._execute_action(step_data, screenshot_path, image.size)
                else:
                    tool_result, start_point, end_point = self._execute_action(step_data, screenshot_path, image.size, task)
            else:
                logging.info("任务已完成，无需操作")

            record = {
                "round": self.current_round,
                "status": status,
                "action": step_data.action,
                "target_element_description": step_data.target_element_description,
                "center": (centerX, centerY) if step_data.action in ["Click", "InputText", "LongClick"] else None,
                "key_points": (start_point, end_point) if step_data.action in ["Fling", "Drag"] else None,
                "tool_result": tool_result
            }

            if status == "Done" and step_data.summary:
                record["summary"] = step_data.summary
                logging.info(f"任务总结: {step_data.summary}")
            self.history.append(record)
            self._log_operation(record)
            self._log_prompt(record, prompt)
            self.history = self.history[-self.history_limit:]

            time.sleep(1)

        if self.current_round == self.max_rounds and status == "Ongoing":
            logging.warning(f"已达到最大轮数（{self.max_rounds}轮），但任务尚未完成。")


if __name__ == "__main__":
    # user_task = ("打开哔哩哔哩搜索HuaWei，注意只能输入英文，我的输入法不支持中文，然后点击一个搜索结果，滑动点击第二个视频")
    user_task = ("打开微博，点击搜索到的第一个用户头像")
    # user_task = ("下滑打开状态栏，关闭蓝牙")
    # user_task = ("打开QQ，给 我的电脑 发送'hello'")
    # openai_client = OpenAI(base_url="https://dashscope.aliyuncs.com/compatible-mode/v1", api_key="sk-5d9f74854f9441f8b7729bd4070e8aaa")
    # openai_client = OpenAI(base_url="https://openrouter.ai/api/v1", api_key="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")
    # openai_client = OpenAI(base_url="https://us.vveai.com/v1", api_key="sk-PQrF3vHODICmiIts83473c856dAf4840BdA67bE67964D889")
    openai_client = OpenAI(base_url="https://dashscope.aliyuncs.com/compatible-mode/v1", api_key="sk-397dece0bf07419d9a33b95ea038ca1d")
    # model_name = "qwen/qwen2.5-vl-72b-instruct"
    # model_name = "qwen2.5-vl-32b-instruct"
    model_name = "qwen-vl-max"
    # model_name = "Qwen2.5-VL-32B-Instruct"
    print("model_name:", model_name)
    
    try:
        agent = AppUseAgent(openai_client=openai_client, output_base_dir="agent_runs_Qwen72b_openrouter", model_name=model_name)
        print("开始执行任务...")
        start_time = time.time()
        agent.run(user_task)
        end_time = time.time()
        duration = end_time - start_time
        print(f"任务执行完成，耗时：{duration:.2f} 秒")
    except Exception as e:
        print(f"Error occurred: {e}. Retrying in 5 seconds...")
        time.sleep(5)
        agent = AppUseAgent(openai_client=openai_client, output_base_dir="agent_runs_Qwen72b_openrouter", model_name=model_name)
        print("开始执行任务（重试）...")
        start_time = time.time()
        agent.run(user_task)
        end_time = time.time()
        duration = end_time - start_time
        print(f"任务执行完成（重试），耗时：{duration:.2f} 秒")



