""" 本文件用于实现从uidumper/yolo处的grounding接口 """
from tooluse import AndroidDevice
from typing import Dict, <PERSON><PERSON>
import torch
from ultralytics import YOLO
from PIL import Image
from torchvision.ops import box_convert
from grounding.util.box_annotator import BoxAnnotator
import numpy as np
from typing import List, Union,Tuple
from grounding.util.utils import predict_yolo,int_box_area,annotate, get_yolo_model

device = 'cpu'
model_path = 'grounding/ours_yolo_model/yolov8.pt'
som_model = get_yolo_model(model_path)
som_model.to(device)
sample_image_size = [2720,1260]
box_overlay_ratio = max(sample_image_size) / 3200
draw_bbox_config = {
    'text_scale': 0.8 * box_overlay_ratio,
    'text_thickness': max(int(2 * box_overlay_ratio), 1),
    'text_padding': max(int(3 * box_overlay_ratio), 1),
    'thickness': max(int(3 * box_overlay_ratio), 1),
}

# 使用yolo获取bbox的方式
def process_image() -> Tuple[Image.Image, Dict]:
    """ 
    使用YOLO方式的grounding
    """
    # 获取截图
    mobile_device = AndroidDevice.getFirstDevice()
    screenshot = mobile_device.get_screenshot()

    # 处理图片
    image = screenshot
    w, h = image.size
    imgsz = (h, w)
    
    # 预测
    xyxy, logits, phrases = predict_yolo(
        model=som_model,
        image=image,
        box_threshold=0.2,
        imgsz=imgsz,
        scale_img=False,
        iou_threshold=0.15
    )
    
    # 归一化
    xyxy = xyxy / torch.Tensor([w, h, w, h]).to(xyxy.device)
    image_np = np.asarray(image)
    
    # 过滤和处理边界框
    phrases = [str(i) for i in range(len(phrases))]
    xyxy_elem = [{'type': 'icon', 'bbox': box, 'interactivity': True, 'content': None} 
                for box in xyxy.tolist() if int_box_area(box, w, h) > 0]
    filtered_boxes_elem = sorted(xyxy_elem, key=lambda x: x['content'] is None)
    filtered_boxes = torch.tensor([box['bbox'] for box in filtered_boxes_elem])
    # 格式转换 (仅在有box时)
    if filtered_boxes.nelement() > 0:
        filtered_boxes = box_convert(boxes=filtered_boxes, in_fmt="xyxy", out_fmt="cxcywh")
    
    # 标注
    phrases = [i for i in range(len(filtered_boxes))]
    annotated_frame, label_coordinates = annotate(
        image_source=image_np,
        boxes=filtered_boxes,
        logits=logits,
        phrases=phrases,
        **draw_bbox_config
    )
    pil_img = Image.fromarray(annotated_frame)

    # 转换图像模式为RGB（如果当前是RGBA）
    if pil_img.mode == 'RGBA':
        pil_img = pil_img.convert('RGB')

    # 转换label_coordinates为所需格式
    converted_dict = []
    for k, v in label_coordinates.items():
        # 获取xywh坐标（左上角x, 左上角y, 宽度, 高度）
        x_left, y_top, width, height = v.tolist() if hasattr(v, 'tolist') else v
        
        # 直接计算右下角坐标
        x_right = x_left + width
        y_bottom = y_top + height
        
        converted_dict.append({
            "id": int(k),
            "bbox": [[x_left, y_top], [x_right, y_bottom]],  # 转换为两点格式
            "type": "detected"
        })

    return [pil_img, converted_dict]


def grounding():
    # 基于ui dumper的
    image,prompt = process_image()
    return [image,str(prompt)]
    # 使用yolo的grounding
